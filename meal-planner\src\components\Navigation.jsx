import React from 'react';
import { Link, useLocation } from 'react-router-dom';

export default function Navigation() {
  const location = useLocation();

  const navItems = [
    { path: '/plats', label: 'Gestion des Plats', icon: '🍽️' },
    { path: '/programme', label: 'Programme Hebdomadaire', icon: '📅' },
    { path: '/suggestion-ia', label: 'Suggestions IA', icon: '🤖' },
  ];

  const isActive = (path) => {
    if (path === '/plats') {
      return location.pathname === '/' || location.pathname === '/plats';
    }
    return location.pathname === path;
  };

  return (
    <nav className="bg-blue-600 text-white shadow-lg">
      <div className="container">
        <div className="flex items-center justify-between py-4">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold">Planificateur Familial</h1>
          </div>
          
          <div className="flex gap-4">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`px-4 py-2 rounded-md transition-colors flex items-center gap-2 ${
                  isActive(item.path)
                    ? 'bg-blue-800 text-white'
                    : 'text-blue-100 hover:bg-blue-700 hover:text-white'
                }`}
              >
                <span>{item.icon}</span>
                <span className="hidden md:inline">{item.label}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
}
