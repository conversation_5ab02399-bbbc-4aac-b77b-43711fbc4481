import { useSelector, useDispatch } from 'react-redux';
import { getDishes, addDish, updateDish, deleteDish, clearDishes } from '../store/dishSlice';

/**
 * Custom hook for dish management
 * Provides easy access to dish state and actions
 */
export const useDishes = () => {
  const dispatch = useDispatch();
  const dishes = useSelector((state) => {
    if (!state || !state.dishes) return [];
    return state.dishes.dishes || [];
  });
  const loading = useSelector((state) => {
    if (!state || !state.dishes) return false;
    return state.dishes.loading || false;
  });
  const error = useSelector((state) => {
    if (!state || !state.dishes) return null;
    return state.dishes.error || null;
  });

  const actions = {
    addDish: (dishData) => dispatch(addDish(dishData)),
    updateDish: (dishData) => dispatch(updateDish(dishData)),
    deleteDish: (dishId) => dispatch(deleteDish(dishId)),
    clearAllDishes: () => dispatch(clearDishes()),
  };

  const utils = {
    getDishById: (id) => dishes.find(dish => dish.id === id),
    getDishesCount: () => dishes.length,
    isDishNameTaken: (name, excludeId = null) => 
      dishes.some(dish => 
        dish.name.toLowerCase() === name.toLowerCase() && 
        dish.id !== excludeId
      ),
  };

  return {
    dishes,
    loading,
    error,
    ...actions,
    ...utils,
  };
};
