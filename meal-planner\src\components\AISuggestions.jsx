import React, { useState } from 'react';
import { useAISuggestions } from '../hooks/useAISuggestions';

export default function AISuggestions() {
  const [preferences, setPreferences] = useState('');
  const { 
    suggestions, 
    loading, 
    error, 
    getSuggestions, 
    clearSuggestions, 
    clearError 
  } = useAISuggestions();

  const handleSubmit = (e) => {
    e.preventDefault();
    getSuggestions(preferences);
  };

  const handleClear = () => {
    setPreferences('');
    clearSuggestions();
    clearError();
  };

  const examplePreferences = [
    "Cuisine Camerounaise",
    "plats simples",
    "sans piment",
    "plats vegetariens",
    
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">
        Suggestions IA pour vos repas
      </h2>
      
      <p className="text-gray-600 mb-6">
        Décrivez vos envies, préférences alimentaires ou contraintes, et notre IA vous proposera des idées de plats personnalisées.
      </p>

      <form onSubmit={handleSubmit} className="mb-6">
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vos préférences alimentaires
          </label>
          <textarea
            value={preferences}
            onChange={(e) => setPreferences(e.target.value)}
            placeholder="Ex: Je cherche des plats végétariens rapides à préparer pour le dîner..."
            rows={4}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
        </div>

        <div className="flex gap-2 mb-4">
          <button
            type="submit"
            disabled={loading || !preferences.trim()}
            className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {loading ? (
              <>
                <span className="animate-spin">⏳</span>
                Génération en cours...
              </>
            ) : (
              <>
                🤖 Obtenir des suggestions
              </>
            )}
          </button>
          
          <button
            type="button"
            onClick={handleClear}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Effacer
          </button>
        </div>
      </form>

      {/* Example preferences */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 mb-2">Exemples de préférences :</p>
        <div className="flex flex-wrap gap-2">
          {examplePreferences.map((example, index) => (
            <button
              key={index}
              onClick={() => setPreferences(example)}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
              disabled={loading}
            >
              {example}
            </button>
          ))}
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center gap-2">
            <span className="text-red-500">❌</span>
            <p className="text-red-700 font-medium">Erreur</p>
          </div>
          <p className="text-red-600 mt-1">{error}</p>
          <button
            onClick={clearError}
            className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
          >
            Fermer
          </button>
        </div>
      )}

      {/* Suggestions display */}
      {suggestions && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-green-500">✨</span>
            <h3 className="font-semibold text-green-800">Suggestions personnalisées</h3>
          </div>
          <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">
            {suggestions}
          </div>
          <div className="mt-4 pt-3 border-t border-green-200">
            <p className="text-sm text-green-600">
              💡 Vous pouvez ajouter ces plats à votre liste dans la section "Gestion des Plats"
            </p>
          </div>
        </div>
      )}

      {/* Loading state */}
      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin text-4xl mb-4">🤖</div>
          <p className="text-gray-600">L'IA génère vos suggestions personnalisées...</p>
          <p className="text-sm text-gray-500 mt-2">Cela peut prendre quelques secondes</p>
        </div>
      )}
    </div>
  );
}
