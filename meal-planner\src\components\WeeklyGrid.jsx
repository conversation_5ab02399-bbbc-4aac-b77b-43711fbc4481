import React from 'react';
import { usePlanning } from '../hooks/usePlanning';
import { useDishes } from '../hooks/useDishes';

export default function WeeklyGrid() {
  const {
    weeklyPlanning,
    daysOfWeek,
    mealTypes,
    setMeal,
    clearMeal,
    clearAllPlanning,
    getPlannedMealsCount
  } = usePlanning();
  
  const { dishes, getDishById } = useDishes();

  const handleMealChange = (day, mealType, dishId) => {
    if (dishId === '') {
      clearMeal(day, mealType);
    } else {
      setMeal(day, mealType, parseInt(dishId));
    }
  };

  const getDishName = (dishId) => {
    if (!dishId) return '';
    const dish = getDishById(dishId);
    return dish ? dish.name : 'Plat introuvable';
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          Programme Hebdomadaire
        </h2>
        <div className="text-sm text-gray-600">
          {getPlannedMealsCount()} repas programmés sur {daysOfWeek.length * mealTypes.length}
        </div>
      </div>

      {dishes.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">
            Aucun plat disponible pour la planification.
          </p>
          <p className="text-sm text-gray-400">
            Ajoutez des plats dans la section "Gestion des Plats" pour commencer à planifier vos repas.
          </p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="border border-gray-300 p-3 bg-gray-50 text-left font-semibold">
                  Jour
                </th>
                {mealTypes.map((mealType) => (
                  <th 
                    key={mealType}
                    className="border border-gray-300 p-3 bg-gray-50 text-center font-semibold min-w-48"
                  >
                    {mealType}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {daysOfWeek.map((day) => (
                <tr key={day}>
                  <td className="border border-gray-300 p-3 font-medium bg-blue-50">
                    {day}
                  </td>
                  {mealTypes.map((mealType) => {
                    const selectedDishId = weeklyPlanning[day]?.[mealType];
                    
                    return (
                      <td key={`${day}-${mealType}`} className="border border-gray-300 p-2">
                        <select
                          value={selectedDishId || ''}
                          onChange={(e) => handleMealChange(day, mealType, e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        >
                          <option value="">-- Choisir un plat --</option>
                          {dishes.map((dish) => (
                            <option key={dish.id} value={dish.id}>
                              {dish.name}
                            </option>
                          ))}
                        </select>
                        {selectedDishId && (
                          <div className="mt-1 text-xs text-gray-600">
                            {getDishById(selectedDishId)?.description?.substring(0, 50)}...
                          </div>
                        )}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-6 flex justify-between items-center">
        <div className="text-sm text-gray-600">
          💡 Astuce: Sélectionnez un plat dans chaque case pour planifier vos repas de la semaine
        </div>
        <button
          onClick={() => {
            if (window.confirm('Êtes-vous sûr de vouloir effacer tout le programme ?')) {
              clearAllPlanning();
            }
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-sm"
        >
          Effacer tout
        </button>
      </div>
    </div>
  );
}
