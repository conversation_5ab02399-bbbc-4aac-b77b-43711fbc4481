"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _classPrivateFieldGet;
var _classApplyDescriptorGet = require("classApplyDescriptorGet");
var _classPrivateFieldGet2 = require("classPrivateFieldGet2");
function _classPrivateFieldGet(receiver, privateMap) {
  var descriptor = _classPrivateFieldGet2(privateMap, receiver);
  return _classApplyDescriptorGet(receiver, descriptor);
}

//# sourceMappingURL=classPrivateFieldGet.js.map
