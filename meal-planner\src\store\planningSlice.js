import { createSlice } from "@reduxjs/toolkit";

const daysOfWeek = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'];
const mealTypes = ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'];

// Initialize empty planning grid
const initializePlanning = () => {
  const planning = {};
  daysOfWeek.forEach(day => {
    planning[day] = {};
    mealTypes.forEach(mealType => {
      planning[day][mealType] = null; // null means no dish selected
    });
  });
  return planning;
};

const planningSlice = createSlice({
  name: "planning",
  initialState: {
    weeklyPlanning: initializePlanning(),
    loading: false,
    error: null,
  },
  reducers: {
    setMealForSlot: (state, action) => {
      const { day, mealType, dishId } = action.payload;
      if (state.weeklyPlanning[day] && mealTypes.includes(mealType)) {
        state.weeklyPlanning[day][mealType] = dishId;
      }
    },
    clearMealFromSlot: (state, action) => {
      const { day, mealType } = action.payload;
      if (state.weeklyPlanning[day] && mealTypes.includes(mealType)) {
        state.weeklyPlanning[day][mealType] = null;
      }
    },
    clearAllPlanning: (state) => {
      state.weeklyPlanning = initializePlanning();
    },
    resetWeeklyPlanning: (state) => {
      state.weeklyPlanning = initializePlanning();
    }
  },
  selectors: {
    getWeeklyPlanning: (state) => state.weeklyPlanning || initializePlanning(),
    getMealForSlot: (state, day, mealType) => {
      return state.weeklyPlanning?.[day]?.[mealType] || null;
    },
    getDaysOfWeek: () => daysOfWeek,
    getMealTypes: () => mealTypes,
  },
});

// Export selectors with proper state path binding
export const getWeeklyPlanning = (state) => planningSlice.selectors.getWeeklyPlanning(state.planning);
export const getMealForSlot = (state, day, mealType) => planningSlice.selectors.getMealForSlot(state.planning, day, mealType);
export const getDaysOfWeek = planningSlice.selectors.getDaysOfWeek;
export const getMealTypes = planningSlice.selectors.getMealTypes;

export const { 
  setMealForSlot, 
  clearMealFromSlot, 
  clearAllPlanning, 
  resetWeeklyPlanning 
} = planningSlice.actions;

export default planningSlice.reducer;
