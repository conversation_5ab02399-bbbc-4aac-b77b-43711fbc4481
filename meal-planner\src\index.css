:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
  color: #333;
}

/* Basic utility classes */
.min-h-screen { min-height: 100vh; }
.bg-gray-100 { background-color: #f5f5f5; }
.bg-white { background-color: white; }
.bg-blue-600 { background-color: #2563eb; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-green-100 { background-color: #dcfce7; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-yellow-50 { background-color: #fefce8; }
.bg-yellow-200 { background-color: #fde047; }
.bg-red-50 { background-color: #fef2f2; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-800 { background-color: #1f2937; }
.bg-purple-50 { background-color: #faf5ff; }
.bg-purple-100 { background-color: #e9d5ff; }

.text-white { color: white; }
.text-gray-800 { color: #1f2937; }
.text-gray-700 { color: #374151; }
.text-gray-600 { color: #4b5563; }
.text-gray-500 { color: #6b7280; }
.text-blue-600 { color: #2563eb; }
.text-blue-800 { color: #1e40af; }
.text-blue-100 { color: #dbeafe; }
.text-green-600 { color: #16a34a; }
.text-green-800 { color: #166534; }
.text-yellow-600 { color: #ca8a04; }
.text-yellow-700 { color: #a16207; }
.text-yellow-800 { color: #854d0e; }
.text-red-500 { color: #ef4444; }
.text-red-600 { color: #dc2626; }
.text-red-700 { color: #b91c1c; }
.text-red-800 { color: #991b1b; }
.text-purple-600 { color: #9333ea; }
.text-purple-700 { color: #7c3aed; }
.text-purple-800 { color: #6b21a8; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.pt-4 { padding-top: 1rem; }

.m-0 { margin: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-8 { margin-top: 2rem; }
.mr-2 { margin-right: 0.5rem; }

.w-full { width: 100%; }
.w-32 { width: 8rem; }
.w-2 { width: 0.5rem; }
.h-2 { height: 0.5rem; }

.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }

.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

.border { border-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-yellow-200 { border-color: #fde047; }
.border-red-200 { border-color: #fecaca; }

.shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }

.block { display: block; }
.cursor-pointer { cursor: pointer; }

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Form styles */
input, select, textarea {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem;
  font-size: 1rem;
  width: 100%;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

button {
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bg-blue-500:hover { background-color: #2563eb; }
.bg-blue-600:hover { background-color: #1d4ed8; }
.bg-gray-500 { background-color: #6b7280; }
.bg-gray-500:hover { background-color: #4b5563; }
.bg-red-500 { background-color: #ef4444; }
.bg-red-500:hover { background-color: #dc2626; }
.bg-green-500 { background-color: #10b981; }
.bg-green-500:hover { background-color: #059669; }
.bg-green-600:hover { background-color: #047857; }
.bg-purple-500 { background-color: #a855f7; }
.bg-purple-500:hover { background-color: #9333ea; }

.transition-colors { transition: background-color 0.2s, color 0.2s; }
.transition-shadow { transition: box-shadow 0.2s; }

.hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }

.text-center { text-align: center; }
.text-left { text-align: left; }

.min-w-48 { min-width: 12rem; }
.animate-spin { animation: spin 1s linear infinite; }

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.whitespace-pre-wrap { white-space: pre-wrap; }
.leading-relaxed { line-height: 1.625; }
.overflow-x-auto { overflow-x: auto; }
.border-collapse { border-collapse: collapse; }

/* Responsive */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}
