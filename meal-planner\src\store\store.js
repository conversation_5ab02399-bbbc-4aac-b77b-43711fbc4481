import { combineReducers, configureStore } from "@reduxjs/toolkit";
import dishSlice from "./dishSlice";
import planningSlice from "./planningSlice";
import storage from "redux-persist/lib/storage";
import { persistReducer, persistStore } from "redux-persist";

const persistConfig = {
  key: 'family-meal-planner-v2',
  storage,
}

const rootReducer = combineReducers({
  dishes: dishSlice,
  planning: planningSlice,
})

const persistedReducer = persistReducer(persistConfig, rootReducer)

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export default store;
export const persistor = persistStore(store);
