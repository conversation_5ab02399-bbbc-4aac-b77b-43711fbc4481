import React from 'react';
import { useDishes } from '../hooks/useDishes';
import { usePlanning } from '../hooks/usePlanning';

export default function DishList({ onEditDish }) {
  const { dishes, deleteDish } = useDishes();
  const { isDishUsedInPlanning } = usePlanning();

  const handleDelete = (dish) => {
    if (isDishUsedInPlanning(dish.id)) {
      if (!window.confirm(
        `Le plat "${dish.name}" est utilisé dans votre programme hebdomadaire. Êtes-vous sûr de vouloir le supprimer ?`
      )) {
        return;
      }
    } else {
      if (!window.confirm(`Êtes-vous sûr de vouloir supprimer "${dish.name}" ?`)) {
        return;
      }
    }
    deleteDish(dish.id);
  };

  if (dishes.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4 text-gray-800">
          Liste des plats (0)
        </h2>
        <p className="text-gray-500 text-center py-8">
          Aucun plat ajouté. Utilisez le formulaire ci-dessus pour ajouter votre premier plat.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">
        Liste des plats ({dishes.length})
      </h2>

      <div className="space-y-4">
        {dishes.map((dish) => {
          const isUsedInPlanning = isDishUsedInPlanning(dish.id);
          
          return (
            <div 
              key={dish.id} 
              className={`border rounded-lg p-4 hover:shadow-md transition-shadow ${
                isUsedInPlanning ? 'border-green-300 bg-green-50' : 'border-gray-200'
              }`}
            >
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-xl font-semibold text-gray-800">
                      {dish.name}
                    </h3>
                    {isUsedInPlanning && (
                      <span className="px-2 py-1 bg-green-200 text-green-800 text-xs rounded-full">
                        Programmé
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 mb-2">
                    {dish.description}
                  </p>
                  <p className="text-sm text-gray-500">
                    Créé le: {dish.createdAt}
                  </p>
                </div>

                <div className="flex gap-2 ml-4">
                  <button
                    onClick={() => onEditDish(dish)}
                    className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                  >
                    Modifier
                  </button>
                  <button
                    onClick={() => handleDelete(dish)}
                    className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors"
                  >
                    Supprimer
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
