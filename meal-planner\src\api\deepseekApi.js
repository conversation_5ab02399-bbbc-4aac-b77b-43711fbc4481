// Configuration de l'API de DeepSeek pour les suggestions
const DEEPSEEK_API_KEY = '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'; // cle d'api
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

/**
 * Get AI meal suggestions based on user preferences
 * @param {string} preferences 
 * @returns {Promise<string>} 
 */
export const getMealSuggestions = async (preferences) => {
  try {
    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Meal Planner App'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-r1',
        messages: [
          {
            role: 'system',
            content: 'Tu es un assistant culinaire Camerounais. Réponds uniquement en français. Suggère des plats simples et délicieux basés sur les préférences de l\'utilisateur. Privilegie des plats aficains et camerounais. Donne 3-5 suggestions avec de brèves descriptions.'
          },
          {
            role: 'user',
            content: `Suggère-moi des plats basés sur ces préférences: ${preferences}`
          }
        ],
        max_tokens: 500,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Réponse API invalide');
    }

    return data.choices[0].message.content; //on retourne uniquement le premier contenu de la réponse en tableau
  } catch (error) {
    console.error('Erreur lors de l\'appel à l\'API DeepSeek:', error);
    throw new Error(`Impossible d'obtenir des suggestions: ${error.message}`);
  }
};
