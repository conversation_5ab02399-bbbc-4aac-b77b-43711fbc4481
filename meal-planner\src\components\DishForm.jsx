import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useDishes } from '../hooks/useDishes';

const schema = yup.object({
  name: yup
    .string()
    .required('Le nom du plat est requis')
    .min(3, 'Le nom doit contenir au moins 3 caractères')
    .max(50, 'Le nom ne peut pas dépasser 50 caractères'),
  description: yup
    .string()
    .required('La description est requise')
    .min(10, 'La description doit contenir au moins 10 caractères')
    .max(200, 'La description ne peut pas dépasser 200 caractères'),
});

export default function DishForm({ editingDish, onEditComplete }) {
  const { addDish, updateDish, isDishNameTaken } = useDishes();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setError,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  useEffect(() => {
    if (editingDish) {
      reset({
        name: editingDish.name,
        description: editingDish.description,
      });
    }
  }, [editingDish, reset]);

  const onSubmit = (data) => {
    // Check for duplicate names
    if (isDishNameTaken(data.name, editingDish?.id)) {
      setError('name', {
        type: 'manual',
        message: 'Un plat avec ce nom existe déjà',
      });
      return;
    }

    if (editingDish) {
      updateDish({
        id: editingDish.id,
        ...data,
      });
      onEditComplete && onEditComplete();
    } else {
      addDish(data);
    }

    reset({
      name: '',
      description: '',
    });
  };

  const handleClear = () => {
    reset({
      name: '',
      description: '',
    });
    onEditComplete && onEditComplete();
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md mb-6">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">
        {editingDish ? 'Modifier le plat' : 'Ajouter un nouveau plat'}
      </h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nom du plat *
          </label>
          <input
            {...register('name')}
            type="text"
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Ex: Spaghetti Bolognaise"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description *
          </label>
          <textarea
            {...register('description')}
            rows={3}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Décrivez le plat, ses ingrédients principaux..."
          />
          {errors.description && (
            <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
          )}
        </div>

        <div className="flex gap-2 pt-4">
          <button
            type="submit"
            className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            {editingDish ? 'Mettre à jour' : 'Ajouter'}
          </button>

          <button
            type="button"
            onClick={handleClear}
            className="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Annuler
          </button>
        </div>
      </form>
    </div>
  );
}
