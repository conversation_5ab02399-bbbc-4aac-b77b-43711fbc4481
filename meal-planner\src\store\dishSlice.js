import { createSlice } from "@reduxjs/toolkit";

const dishSlice = createSlice({
  name: "dishes",
  initialState: {
    dishes: [
      {
        id: 1,
        name: "Spaghetti Bolognaise",
        description: "Délicieux spaghetti avec une sauce bolognaise maison riche en saveurs",
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 2,
        name: "Salade César",
        description: "Salade fraîche avec laitue, parmesan, croûtons et sauce césar crémeuse",
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 3,
        name: "<PERSON><PERSON> Rôti",
        description: "Poulet entier rôti aux herbes de Provence avec légumes de saison",
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 4,
        name: "Quiche Lorraine",
        description: "Quiche traditionnelle avec lardons, œufs et crème fraîche dans une pâte brisée",
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 5,
        name: "<PERSON><PERSON><PERSON><PERSON>",
        description: "Mélange de légumes méditerranéens mijotés avec tomates, courgettes et aubergines",
        createdAt: (new Date()).toLocaleString()
      }
    ],
    loading: false,
    error: null,
  },
  reducers: {
    addDish: (state, action) => {
      const newId = state.dishes.length > 0 ? Math.max(...state.dishes.map(d => d.id)) + 1 : 1;
      state.dishes.push({
        id: newId,
        ...action.payload,
        createdAt: (new Date()).toLocaleString(),
      });
    },
    updateDish: (state, action) => {
      const { id, name, description } = action.payload;
      const dish = state.dishes.find(dish => dish.id === id);
      if (dish) {
        dish.name = name;
        dish.description = description;
      }
    },
    deleteDish: (state, action) => {
      state.dishes = state.dishes.filter(dish => dish.id !== action.payload);
    },
    clearDishes: (state) => {
      state.dishes = [];
    },
  },
  selectors: {
    getDishes: (state) => state.dishes || [],
    getDishById: (state, id) => (state.dishes || []).find(dish => dish.id === id),
    getDishesCount: (state) => (state.dishes || []).length,
  },
});

export const { getDishes, getDishById, getDishesCount } = dishSlice.selectors;
export const { addDish, updateDish, deleteDish, clearDishes } = dishSlice.actions;
export default dishSlice.reducer;
