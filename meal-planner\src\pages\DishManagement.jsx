import React, { useState } from 'react';
import DishForm from '../components/DishForm';
import DishList from '../components/DishList';
import { useDishes } from '../hooks/useDishes';

export default function DishManagement() {
  const [editingDish, setEditingDish] = useState(null);
  const { dishes } = useDishes();

  const handleEditDish = (dish) => {
    setEditingDish(dish);
  };

  const handleEditComplete = () => {
    setEditingDish(null);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container py-6 space-y-6">
        {/* Page Header */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Gestion des Plats
          </h1>
          <p className="text-gray-600">
            Ajou<PERSON>z, modifiez et gérez votre collection de plats pour la planification familiale.
          </p>
          
          {/* Statistics */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-800">Total Plats</h3>
              <p className="text-3xl font-bold text-blue-600">{dishes.length}</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-green-800">Prêt à planifier</h3>
              <p className="text-3xl font-bold text-green-600">
                {dishes.length >= 7 ? '✓' : dishes.length + '/7'}
              </p>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-yellow-800">Statut</h3>
              <p className="text-sm font-medium text-yellow-600">
                {dishes.length === 0 
                  ? 'Commencez par ajouter des plats' 
                  : dishes.length < 7 
                    ? 'Ajoutez plus de plats pour une semaine complète'
                    : 'Collection complète pour planifier'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Dish Form */}
        <DishForm 
          editingDish={editingDish} 
          onEditComplete={handleEditComplete}
        />

        {/* Dish List */}
        <DishList onEditDish={handleEditDish} />

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-800 mb-2">💡 Conseils</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Ajoutez au moins 7 plats pour planifier une semaine complète</li>
            <li>• Utilisez des descriptions détaillées pour vous rappeler des ingrédients</li>
            <li>• Les plats marqués "Programmé" sont utilisés dans votre planning hebdomadaire</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
