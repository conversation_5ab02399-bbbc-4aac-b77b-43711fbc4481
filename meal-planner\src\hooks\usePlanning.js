import { useSelector, useDispatch } from 'react-redux';
import { 
  getWeeklyPlanning, 
  getDaysOfWeek, 
  getMealTypes,
  setMealForSlot, 
  clearMealFromSlot, 
  clearAllPlanning 
} from '../store/planningSlice';

/**
 * Custom hook for weekly meal planning
 * Provides easy access to planning state and actions
 */
export const usePlanning = () => {
  const dispatch = useDispatch();

  // Use the slice selector correctly by passing the slice state
  const weeklyPlanning = useSelector((state) => {
    try {
      // Handle case where state is completely undefined/null
      if (!state) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('usePlanning - Redux state is null/undefined');
        }
        return {};
      }

      // Handle case where state exists but planning slice is missing
      if (!state.planning) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('usePlanning - planning slice missing, available keys:', Object.keys(state || {}));
        }
        return {};
      }

      // Use the slice selector with the slice state
      return getWeeklyPlanning(state.planning);
    } catch (error) {
      console.error('Error in weeklyPlanning selector:', error);
      return {};
    }
  });

  const loading = useSelector((state) => {
    try {
      if (!state || !state.planning) return false;
      return state.planning.loading || false;
    } catch (error) {
      console.error('Error in loading selector:', error);
      return false;
    }
  });

  const error = useSelector((state) => {
    try {
      if (!state || !state.planning) return null;
      return state.planning.error || null;
    } catch (error) {
      console.error('Error in error selector:', error);
      return null;
    }
  });

  // Use slice selectors for consistency
  const daysOfWeek = getDaysOfWeek();
  const mealTypes = getMealTypes();

  const actions = {
    setMeal: (day, mealType, dishId) => 
      dispatch(setMealForSlot({ day, mealType, dishId })),
    clearMeal: (day, mealType) => 
      dispatch(clearMealFromSlot({ day, mealType })),
    clearAllPlanning: () => dispatch(clearAllPlanning()),
  };

  const utils = {
    getMealForSlot: (day, mealType) => {
      if (!weeklyPlanning || typeof weeklyPlanning !== 'object') return null;
      return weeklyPlanning[day]?.[mealType] || null;
    },
    getPlannedMealsCount: () => {
      if (!weeklyPlanning || typeof weeklyPlanning !== 'object') return 0;
      let count = 0;
      daysOfWeek.forEach(day => {
        mealTypes.forEach(mealType => {
          if (weeklyPlanning[day]?.[mealType]) count++;
        });
      });
      return count;
    },
    isDishUsedInPlanning: (dishId) => {
      if (!weeklyPlanning || typeof weeklyPlanning !== 'object' || !dishId) return false;
      try {
        return daysOfWeek.some(day =>
          mealTypes.some(mealType =>
            weeklyPlanning[day]?.[mealType] === dishId
          )
        );
      } catch (error) {
        console.error('Error in isDishUsedInPlanning:', error);
        return false;
      }
    },
  };

  return {
    weeklyPlanning,
    daysOfWeek,
    mealTypes,
    loading,
    error,
    ...actions,
    ...utils,
  };
};
