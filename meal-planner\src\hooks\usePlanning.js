import { useSelector, useDispatch } from 'react-redux';
import { 
  getWeeklyPlanning, 
  getDaysOfWeek, 
  getMealTypes,
  setMealForSlot, 
  clearMealFromSlot, 
  clearAllPlanning 
} from '../store/planningSlice';

/**
 * Custom hook for weekly meal planning
 * Provides easy access to planning state and actions
 */
export const usePlanning = () => {
  const dispatch = useDispatch();
  const weeklyPlanning = useSelector((state) => {
    if (!state || !state.planning) return {};
    return state.planning.weeklyPlanning || {};
  });
  const loading = useSelector((state) => {
    if (!state || !state.planning) return false;
    return state.planning.loading || false;
  });
  const error = useSelector((state) => {
    if (!state || !state.planning) return null;
    return state.planning.error || null;
  });

  const daysOfWeek = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'];
  const mealTypes = ['Déjeuner', 'Dîner'];

  const actions = {
    setMeal: (day, mealType, dishId) => 
      dispatch(setMealForSlot({ day, mealType, dishId })),
    clearMeal: (day, mealType) => 
      dispatch(clearMealFromSlot({ day, mealType })),
    clearAllPlanning: () => dispatch(clearAllPlanning()),
  };

  const utils = {
    getMealForSlot: (day, mealType) => weeklyPlanning[day]?.[mealType] || null,
    getPlannedMealsCount: () => {
      let count = 0;
      daysOfWeek.forEach(day => {
        mealTypes.forEach(mealType => {
          if (weeklyPlanning[day]?.[mealType]) count++;
        });
      });
      return count;
    },
    isDishUsedInPlanning: (dishId) => {
      if (!weeklyPlanning || !dishId) return false;
      return daysOfWeek.some(day =>
        mealTypes.some(mealType =>
          weeklyPlanning[day]?.[mealType] === dishId
        )
      );
    },
  };

  return {
    weeklyPlanning,
    daysOfWeek,
    mealTypes,
    loading,
    error,
    ...actions,
    ...utils,
  };
};
