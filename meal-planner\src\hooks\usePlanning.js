import { useSelector, useDispatch } from 'react-redux';
import { createSelector } from '@reduxjs/toolkit';
import {
  getWeeklyPlanning,
  getDaysOfWeek,
  getMealTypes,
  setMealForSlot,
  clearMealFromSlot,
  clearAllPlanning
} from '../store/planningSlice';

// Memoized selectors with proper transformation to avoid pass-through warnings
const selectWeeklyPlanning = createSelector(
  [(state) => state.planning?.weeklyPlanning],
  (weeklyPlanning) => {
    // Ensure we always return a valid object structure
    if (!weeklyPlanning || typeof weeklyPlanning !== 'object') {
      return {};
    }
    return weeklyPlanning;
  }
);

const selectPlanningLoading = createSelector(
  [(state) => state.planning?.loading],
  (loading) => Boolean(loading) // Transform to boolean to avoid pass-through
);

const selectPlanningError = createSelector(
  [(state) => state.planning?.error],
  (error) => error ? String(error) : null // Transform to string or null to avoid pass-through
);

/**
 * Custom hook for weekly meal planning
 * Provides easy access to planning state and actions
 */
export const usePlanning = () => {
  const dispatch = useDispatch();

  // Use memoized selectors to prevent unnecessary re-renders
  const weeklyPlanning = useSelector(selectWeeklyPlanning);
  const loading = useSelector(selectPlanningLoading);
  const error = useSelector(selectPlanningError);

  // Use slice selectors for static data - these don't need useSelector
  const daysOfWeek = getDaysOfWeek();
  const mealTypes = getMealTypes();

  const actions = {
    setMeal: (day, mealType, dishId) => 
      dispatch(setMealForSlot({ day, mealType, dishId })),
    clearMeal: (day, mealType) => 
      dispatch(clearMealFromSlot({ day, mealType })),
    clearAllPlanning: () => dispatch(clearAllPlanning()),
  };

  const utils = {
    getMealForSlot: (day, mealType) => {
      if (!weeklyPlanning || typeof weeklyPlanning !== 'object') return null;
      return weeklyPlanning[day]?.[mealType] || null;
    },
    getPlannedMealsCount: () => {
      if (!weeklyPlanning || typeof weeklyPlanning !== 'object') return 0;
      let count = 0;
      daysOfWeek.forEach(day => {
        mealTypes.forEach(mealType => {
          if (weeklyPlanning[day]?.[mealType]) count++;
        });
      });
      return count;
    },
    isDishUsedInPlanning: (dishId) => {
      if (!weeklyPlanning || typeof weeklyPlanning !== 'object' || !dishId) return false;
      try {
        return daysOfWeek.some(day =>
          mealTypes.some(mealType =>
            weeklyPlanning[day]?.[mealType] === dishId
          )
        );
      } catch (error) {
        console.error('Error in isDishUsedInPlanning:', error);
        return false;
      }
    },
  };

  return {
    weeklyPlanning,
    daysOfWeek,
    mealTypes,
    loading,
    error,
    ...actions,
    ...utils,
  };
};
