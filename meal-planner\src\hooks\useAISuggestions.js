import { useState } from 'react';
import { getMealSuggestions } from '../api/deepseekApi';

/**
 * Custom hook for AI meal suggestions
 * Handles API calls, loading states, and error handling
 */
export const useAISuggestions = () => {
  const [suggestions, setSuggestions] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const getSuggestions = async (preferences) => {
    if (!preferences || preferences.trim().length === 0) {
      setError('Veuillez entrer vos préférences alimentaires');
      return;
    }

    setLoading(true);
    setError(null);
    setSuggestions('');

    try {
      const result = await getMealSuggestions(preferences);
      setSuggestions(result);
    } catch (err) {
      setError(err.message || 'Une erreur est survenue lors de la génération des suggestions');
      setSuggestions('');
    } finally {
      setLoading(false);
    }
  };

  const clearSuggestions = () => {
    setSuggestions('');
    setError(null);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    suggestions,
    loading,
    error,
    getSuggestions,
    clearSuggestions,
    clearError,
  };
};
